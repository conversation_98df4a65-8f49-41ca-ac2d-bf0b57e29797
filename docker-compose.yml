version: "3.9"

services:
  db:
    image: postgres:15
    container_name: postgres-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: guru
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - ./db-init/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: go-services
    depends_on:
      db:
        condition: service_healthy
    environment:
      DB_URL: ********************************/postgres?sslmode=disable
      PORT: 8080
      SECRET: dsfklafkjdfbnakjdfbbkadfakjfbhbfakjdflabsh
    ports:
      - "8080:8080"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
