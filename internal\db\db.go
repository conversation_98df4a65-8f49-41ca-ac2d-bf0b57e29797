package db

import (
	"log"
	"os"
	"services/internal/config"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// func New(cfg *config.Config) (*gorm.DB, error) {
// 	db, err := gorm.Open(mysql.Open(cfg.DBURL), &gorm.Config{
// 		Logger: gormLogger.New(
// 			log.New(os.Stdout, "\r\n", log.LstdFlags),
// 			gormLogger.Config{
// 				SlowThreshold: time.Second,
// 				LogLevel:      gormLogger.Info,
// 				Colorful:      true,
// 			}),
// 	})
// 	if err != nil {
// 		return nil, err
// 	}

// 	return db, nil
// }

func New(cfg *config.Config) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(cfg.DBURL), &gorm.Config{
		Logger: gormLogger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			gormLogger.Config{
				SlowThreshold: time.Second,
				LogLevel:      gormLogger.Info,
				Colorful:      true,
			}),
	})
	if err != nil {
		log.Println("error on db config", err)
		return nil, err
	}
	// time zone
	if err := db.Exec("SET TIME ZONE 'Asia/Kolkata'").Error; err != nil {
		log.Println("failed to set timezone:", err)
		return nil, err
	}
	return db, nil
}
