package common

import (
	"encoding/json"
	"log"
	"time"

	"golang.org/x/crypto/bcrypt"
)

const (
	Success    = "S"
	Error      = "E"
	Provision  = "provision"
	Start      = "start"
	Stop       = "stop"
	Reboot     = "reboot"
	Terminate  = "terminate"
	Started    = "started"
	Rebooted   = "rebooted"
	Stopped    = "stopped"
	Running    = "running"
	Terminated = "terminated"
	Created    = "created"
	User       = "admin"
	Password   = "admin"
)

type GResponse struct {
	Status string
	Msg    string
}

func NewResponse() *GResponse {
	return &GResponse{Status: Success}
}

func ErrorResponse(data interface{}) string {
	result, err := json.Marshal(data)
	if err != nil {
		return err.Error()
	}
	return string(result)
}

func Ist_time() time.Time {
	istLocation, _ := time.LoadLocation("Asia/Kolkata")
	istNow := time.Now().In(istLocation)
	return istNow
}

type HashedPassword struct {
	Password []byte
}

func NewHashedPassword() *HashedPassword {
	hash, err := bcrypt.GenerateFromPassword([]byte("admin"), 10)
	if err != nil {
		log.Println("error:", err)
	}
	// hashed := someHashFunc(raw) // replace with bcrypt or sha256 etc.
	return &HashedPassword{Password: hash}
}
