package api

import (
	"encoding/binary"
	"errors"
	"log"
	"net"
	"services/internal/helpers"

	"gorm.io/gorm"
)

// converts IP to uint32
func ipToInt(ip net.IP) uint32 {
	ip = ip.To4()
	return binary.BigEndian.Uint32(ip)
}

// Converts uint32 back to IP
func intToIP(n uint32) net.IP {
	ip := make(net.IP, 4)
	binary.BigEndian.PutUint32(ip, n)
	return ip
}

// Get next IP address within your range
func getNextIP(lastIP string, db *gorm.DB, pdebug *helpers.HelperStruct) (string, error) {
	startIP := net.ParseIP("***********")
	endIP := net.ParseIP("***************")

	start := ipToInt(startIP)
	end := ipToInt(endIP)
	var lAllocated_ip Allocated_ips
	var current uint32
	if lastIP == "" {
		current = start
	} else {
		current = ipToInt(net.ParseIP(lastIP)) + 1
	}

	if current > end {
		// fetch the unused IP from the database
		lAllocated_ip, err := FetchFreeIp(db) // Fetch a random free IP if the range is exhausted
		if err != nil {
			pdebug.Log(helpers.Elog, "Error fetching free IP: %v", err)
			return "", helpers.ErrReturn(err)
		}
		return lAllocated_ip.Ip, nil
	}

	nextIP := intToIP(current).String()
	lAllocated_ip.Ip = nextIP
	lAllocated_ip.Allocated = false
	// new IP was Inserted, change the status to true by default
	err := db.Debug().Create(&lAllocated_ip).Error
	if err != nil {
		pdebug.Log(helpers.Elog, "Error in Insert IP: %v", err)
		return "", helpers.ErrReturn(err)
	}
	return nextIP, nil
}

// If IP was Exhausted , fetch the random free IPs
func FetchFreeIp(db *gorm.DB) (Allocated_ips, error) {
	var lIps Allocated_ips
	err := db.Where("allocated = ?", true).First(&lIps).Error
	if err != nil {
		return lIps, errors.New("IP not available")
	}
	log.Println("lIps:", lIps)

	// get the already created unused IP, change the status to false
	err = db.Debug().Table("allocated_ips").Where("ip = ?", lIps.Ip).Update("allocated", false).Error
	if err != nil {
		return lIps, errors.New("Ip status not updated")
	}

	return lIps, nil
}
