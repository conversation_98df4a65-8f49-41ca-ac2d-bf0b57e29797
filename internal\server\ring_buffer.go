package server

import (
	"fmt"
	"sync"
	"time"
)

type ServerId struct {
	ServerName string
	State      string
	Timestamp  time.Time
}

// var DefaultBufferSize = 100
var DefaultBufferSize = 100

func NewServerLogManager() *LogManager[ServerId] {
	return NewLogManager[ServerId](DefaultBufferSize)
}

// Generic RingBuffer as before
type RingBuffer[T any] struct {
	buffer []T
	size   int
	mu     sync.Mutex
	write  int
	count  int
}

func NewRingBuffer[T any](size int) *RingBuffer[T] {
	return &RingBuffer[T]{buffer: make([]T, size), size: size}
}

func (rb *RingBuffer[T]) Add(value T) {
	rb.mu.Lock()
	defer rb.mu.Unlock()

	rb.buffer[rb.write] = value
	rb.write = (rb.write + 1) % rb.size

	if rb.count < rb.size {
		rb.count++
	}
}

func (rb *<PERSON>Buffer[T]) Get() []T {
	rb.mu.Lock()
	defer rb.mu.Unlock()

	result := make([]T, 0, rb.count)
	for i := 0; i < rb.count; i++ {
		index := (rb.write + rb.size - rb.count + i) % rb.size
		result = append(result, rb.buffer[index])
	}
	return result
}

// LogManager with dynamic client support
type LogManager[T any] struct {
	buffers map[string]*RingBuffer[T]
	mu      sync.RWMutex
	size    int
}

func NewLogManager[T any](bufferSize int) *LogManager[T] {
	return &LogManager[T]{
		buffers: make(map[string]*RingBuffer[T]),
		size:    bufferSize,
	}
}

func (lm *LogManager[T]) AddLog(clientID string, log T) {
	lm.mu.RLock()
	rb, exists := lm.buffers[clientID]
	lm.mu.RUnlock()

	if !exists {
		// Upgrade to write lock to create buffer
		lm.mu.Lock()
		// Double-check in case another thread created it
		if rb, exists = lm.buffers[clientID]; !exists {
			rb = NewRingBuffer[T](lm.size)
			lm.buffers[clientID] = rb
		}
		lm.mu.Unlock()
	}

	rb.Add(log)
}

func (lm *LogManager[T]) GetLogs(clientID string) []T {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	if rb, ok := lm.buffers[clientID]; ok {
		return rb.Get()
	}
	return nil
}

func (s ServerId) String() string {
	return fmt.Sprintf("Server: %s, State: %s, Time (IST): %s",
		s.ServerName,
		s.State,
		s.Timestamp.Format("2006-01-02 15:04:05 MST"))
}
