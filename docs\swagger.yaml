basePath: /
definitions:
  api.Allocated_ips:
    properties:
      allocated:
        type: boolean
      id:
        type: integer
      ip:
        type: string
    type: object
  api.LoginRespStruc:
    properties:
      msg:
        example: Login successful
        type: string
      status:
        example: success
        type: string
    type: object
  api.LoginStruc:
    properties:
      password:
        example: admin
        type: string
      user:
        example: admin
        type: string
    type: object
  api.RespStruct:
    properties:
      msg:
        type: string
      servers:
        items:
          $ref: '#/definitions/api.Servers'
        type: array
      status:
        type: string
    type: object
  api.RingRespStruc:
    properties:
      msg:
        type: string
      servers:
        items:
          $ref: '#/definitions/server.ServerId'
        type: array
      status:
        type: string
    type: object
  api.ServerCreateReq:
    properties:
      name:
        example: server-16
        type: string
      region:
        example: us-east
        type: string
      status:
        example: stopped
        type: string
      type:
        example: t2.micro
        type: string
    type: object
  api.Servers:
    properties:
      created_at:
        type: string
      id:
        type: integer
      ip_address:
        $ref: '#/definitions/api.Allocated_ips'
      ip_id:
        description: Ip_address     string `gorm:"type:inet"`
        type: integer
      name:
        type: string
      region:
        type: string
      started_at:
        type: string
      status:
        type: string
      terminated_at:
        type: string
      type:
        type: string
      updated_at:
        type: string
      uptime_seconds:
        type: integer
    type: object
  helpers.ErrorResponse:
    properties:
      msg:
        type: string
      status:
        type: string
      statusCode:
        type: string
    type: object
  server.ServerId:
    properties:
      serverName:
        type: string
      state:
        type: string
      timestamp:
        type: string
    type: object
host: localhost:8080
info:
  contact: {}
  description: Simulates virtual server lifecycle, billing, and state transitions
  title: Spacekayak Virtual Server API
  version: "1.0"
paths:
  /login:
    post:
      consumes:
      - application/json
      description: Authenticates a user and returns login result by default user:admin
        and password:admin
      parameters:
      - description: Login credentials
        in: body
        name: credentials
        required: true
        schema:
          $ref: '#/definitions/api.LoginStruc'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.LoginRespStruc'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/helpers.ErrorResponse'
      summary: User login
      tags:
      - auth
  /server:
    post:
      consumes:
      - application/json
      description: Creates a virtual server with default status "stopped" and allocates
        a private IP
      parameters:
      - description: Server creation payload
        in: body
        name: server
        required: true
        schema:
          $ref: '#/definitions/api.ServerCreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/api.Servers'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Servers'
      summary: Provision a new server
      tags:
      - servers
  /servers:
    get:
      consumes:
      - application/json
      description: Lists all servers with filters for region, status, type; supports
        pagination and sorting
      parameters:
      - description: Filter by region
        in: query
        name: region
        type: string
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by type
        in: query
        name: type
        type: string
      - description: Limit number of results
        in: query
        name: limit
        type: integer
      - description: Offset for pagination
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.RespStruct'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/helpers.ErrorResponse'
      summary: List servers
      tags:
      - servers
  /servers/{id}:
    get:
      consumes:
      - application/json
      description: Retrieves full metadata including uptime
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Servers'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/helpers.ErrorResponse'
      summary: Get server details
      tags:
      - servers
  /servers/{id}/{action}:
    post:
      consumes:
      - application/json
      description: Set a server action like start, stop, reboot, or terminate
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: integer
      - description: Action to perform (start, stop, reboot, terminate)
        in: path
        name: action
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Servers'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/helpers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/helpers.ErrorResponse'
      summary: Set action on server
      tags:
      - servers
  /servers/{id}/logs:
    get:
      consumes:
      - application/json
      description: Retrieves last 100 state transitions (ring buffer)
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.RingRespStruc'
            type: array
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/helpers.ErrorResponse'
      summary: Get server logs
      tags:
      - servers
swagger: "2.0"
