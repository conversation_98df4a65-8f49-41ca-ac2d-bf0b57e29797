package server

import (
	"context"
	"fmt"
	"net/http"
	"services/internal/config"
	"services/internal/helpers"

	"go.uber.org/fx"
)

// RunHTTPServer sets up lifecycle-controlled HTTP server
func RunHTTPServer(lc fx.Lifecycle, cfg *config.Config, handler http.Handler) {
	ldebug := new(helpers.HelperStruct)
	ldebug.Init()

	ldebug.SetReference("RunHTTPServer")
	srv := &http.Server{
		Addr:    ":" + cfg.Port,
		Handler: handler,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			ldebug.Log(helpers.Statement, "Server started (+)")
			go func() {
				if err := srv.ListenAndServe(); err != http.ErrServerClosed {
					// panic(err)
					fmt.Println("error starting server:", err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			ldebug.Log(helpers.Statement, "Server started (-)")
			return srv.Shutdown(ctx)
		},
	})
}
