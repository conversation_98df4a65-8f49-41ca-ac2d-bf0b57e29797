


DROP TABLE IF EXISTS servers;
DROP TABLE IF EXISTS allocated_ips;

CREATE TABLE public.allocated_ips (
	id serial4 NOT NULL,
	ip inet NULL,
	allocated bool DEFAULT true NULL,
	CONSTRAINT allocated_ips_ip_key UNIQUE (ip),
	CONSTRAINT allocated_ips_pkey PRIMARY KEY (id)
);

CREATE TABLE public.servers (
	id serial4 NOT NULL,
	"name" text NOT NULL,
	status text NULL,
	region text NULL,
	"type" text NULL,
	created_at timestamp NULL,
	updated_at timestamp NULL,
	terminated_at timestamp NULL,
	started_at timestamp NULL,
	uptime_seconds int4 NULL,
	ip_id int4 NULL,
	updated_by varchar <PERSON>ULL,
	CONSTRAINT servers_name_unique UNIQUE (name),
	CONSTRAINT servers_pkey PRIMARY KEY (id),
	CONSTRAINT servers_allocated_ips_fk FOREIGN KEY (ip_id) REFERENCES public.allocated_ips(id)
);
