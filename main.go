// @title           Spacekayak Virtual Server API
// @version         1.0
// @description     Simulates virtual server lifecycle, billing, and state transitions
// @host            localhost:8080
// @BasePath        /
package main

// import (
// 	"fmt"
// 	"services/internal/database"
// )

// func main() {
// 	database.LoadEncVariables
// 	database.ConnectToDB()
// 	fmt.Println("Hello, world!")
// 	select {}
// }

import (
	"log"
	"os"
	_ "services/docs"
	"services/internal/api"
	"services/internal/common"
	"services/internal/config"
	"services/internal/db"
	"services/internal/server"
	"time"

	"github.com/joho/godotenv"
	"go.uber.org/fx"
)

// Core HTTP API
// ● POST /server
// Provision a new virtual server.

// ● GET /servers/:id
// Retrieve full metadata (including live uptime and billing).

// ● POST /servers/:id/action
// Actions: start, stop, reboot, terminate.
// Enforce valid FSM transitions; invalid → HTTP 409 Conflict.

// ● GET /servers
// List all servers, filterable by region, status, type; supports pagination (limit,
// offset); sorted (newest first).

// ● GET /servers/:id/logs
// Return last 100 lifecycle events (ring buffer).

func main() {
	if err := os.MkdirAll("./log", 0755); err != nil {
		log.Fatalf("error creating log directory: %v", err)
	}
	lFile, lErr := os.OpenFile("./log/logfile"+time.Now().Format("02012006.15.04.05.000000000")+".txt", os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if lErr != nil {
		log.Fatalf("error opening file: %v", lErr)
	}
	defer lFile.Close()
	log.SetOutput(lFile)

	// load the env file
	err := godotenv.Load(".env")
	if err != nil {
		log.Printf("Error loading .env file: %v", err)
	}
	// startedTime := time.Now().Add(-1 * time.Hour) // 1 hour ago
	// lServer := api.Servers{
	// 	Started_at: &startedTime,
	// }
	// pdebug := new(helpers.HelperStruct)
	// pdebug.Init()
	// Time(lServer, pdebug)

	app := fx.New(
		// Provide dependencies
		fx.Provide(
			config.New, // Config loader
			// logger.New,     // Logger setup
			server.NewServerLogManager,
			common.NewHashedPassword,
			db.New,         // Database connection
			api.NewHandler, // HTTP handler
			common.NewResponse,
		),

		// Start HTTP server
		fx.Invoke(server.RunHTTPServer),
	)

	// Start the app
	app.Run()
}
