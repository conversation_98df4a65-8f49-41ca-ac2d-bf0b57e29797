# Stage 1: Build
FROM golang:1.23.11 AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY . .

# Build the binary named "app" from main.go (adjust path if needed)
RUN CGO_ENABLED=0 GOOS=linux go build -o app main.go

# Stage 2: Runtime
FROM gcr.io/distroless/static:nonroot

# ✅ Correct path to compiled binary
COPY --from=builder /app/app /app


# ✅ Copy .env file into container root (adjust path if your app expects it somewhere else)
COPY --from=builder /app/.env /.env

EXPOSE 8080
USER nonroot:nonroot

ENTRYPOINT ["/app"]
