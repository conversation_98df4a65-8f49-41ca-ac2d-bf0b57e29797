# 🛠️ Runbook for Virtual Server Lifecycle Backend

This runbook provides essential operational procedures for setting up, running, and maintaining the Go-based backend service.

---

## 📦 Components

| Component      | Description                                   |
|----------------|-----------------------------------------------|
| Go App         | Backend API built with Go and Chi             |
| PostgreSQL     | Relational database for server records        |
| Docker Compose | Local orchestration of app and DB            |
| Swagger UI     | Auto-generated OpenAPI docs via swaggo/swag   |

---

## 🚀 Setup & Deployment

### 1. Clone the repository

```bash
git clone https://github.com/GURU292001/spacekayak_assessment.git
cd spacekayak_assessment
```

### 2. Swagger UI

```bash
go install github.com/swaggo/swag/cmd/swag@latest
```
Run at main.go
```bash
swag init 
```

Comment this, to avoid deprecated, present in docs.go
```bash
// LeftDelim:        "{{",
// RightDelim:       "}}",
```
Run at main.go
```bash
go run main.go 
```

Open in Browser
```bash
http://localhost:8080/swagger/index.html

```
## 🐳 Docker Compose Common Commands

### 🔧 Build and Start All Services
```bash
docker-compose up --build
```
Stop All Services
```bash
docker-compose down
```

## 🔍 Observability

### 📘 Logging
-  Log folder has the Structure Logging
-  I used helper package (Custom debugger)
- It Record from the server Start to End









