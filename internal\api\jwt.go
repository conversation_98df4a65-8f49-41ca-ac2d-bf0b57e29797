package api

import (
	"fmt"
	"net/http"
	"os"
	"services/internal/helpers"

	"github.com/golang-jwt/jwt"
)

func (h Handler) JWTAuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ldebug := new(helpers.HelperStruct)
		ldebug.Init()
		ldebug.Log(helpers.Statement, "JWTAuthMiddleware(+)")
		var tokenStr string
		//  From Cookie
		cookie, err := r.<PERSON>("Authorization")
		if err == nil {
			tokenStr = cookie.Value
		}
		// id := chi.URLParam(r, "	id")
		// ldebug.Log(helpers.Statement, fmt.Sprintf("Request path: %s, ID param: %s", r.URL.Path, id))

		if tokenStr == "" {
			// http.Error(w, "Missing token", http.StatusUnauthorized)
			w.WriteHeader(http.StatusUnauthorized)
			fmt.Fprint(w, helpers.GetError_String("Unauthorized", "Missing token"))
			return
		}

		token, err := jwt.Parse(tokenStr, func(t *jwt.Token) (interface{}, error) {
			return []byte(os.Getenv("SECRET")), nil
		})

		if err != nil || !token.Valid {
			// http.Error(w, "Invalid token", http.StatusUnauthorized)
			w.WriteHeader(http.StatusUnauthorized)
			fmt.Fprint(w, helpers.GetError_String("Unauthorized", "Invalid token"))
			return
		}

		_, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			w.WriteHeader(http.StatusUnauthorized)
			fmt.Fprint(w, helpers.GetError_String("Unauthorized", "Invalid claim"))
			return
		}

		// Optional: Attach user info to context
		// ctx := context.WithValue(r.Context(), "user", claims["sub"])
		// ctx = context.WithValue(ctx, "role", claims["role"]) // if you added roles
		// next.ServeHTTP(w, r.WithContext(ctx))
		next.ServeHTTP(w, r)
		ldebug.Log(helpers.Statement, "JWTAuthMiddleware(-)")
	})
}
