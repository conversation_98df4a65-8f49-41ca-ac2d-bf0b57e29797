#  Backend Task

Design & build a backend service simulating virtual server lifecycles—provisioning, state
transitions, billing, logging, observability—without managing real infrastructure.



## 🛠 Tech Stack

- **Go (Golang)** `v1.23.11` – Backend APIs
- **GORM** – ORM for seamless database interactions  
- **Postgres** – Relational database for storing normalized sales data  
- **Rest API** – For communication between frontend and backend


## 🔌 Core HTTP API

### `POST /login (mandatory)`
- Note: Login is mandatory to access protected routes with JWT authentication.



```bash
{
  "user":"admin",
  "password":"admin"
}
```
---


### `POST /server`
Provision a new virtual server.

---

### `GET /servers/:id`
Retrieve full metadata, including live uptime and billing.

---

### `POST /servers/:id/action`
Perform an action on the server.  
**Valid actions**: `start`, `stop`, `reboot`, `terminate`.

- Enforces valid FSM transitions.
- Invalid transitions result in: `HTTP 409 Conflict`

---

### `GET /servers`
List all servers.

- Filterable by: `region`, `status`, `type`
- Supports pagination: `limit`, `offset`
- Sorted by: **newest first**

---

### `GET /servers/:id/logs`
 - Returns the last **100 lifecycle events** using a **ring buffer**.

## 🔌 Bonus Feature

### `⏲️ Idle Reaper`
Automatically terminates **stopped servers** that have been idle for **30+ minutes**.

- Runs as a background scheduler.
- Ensures efficient resource usage.


### `JWT `
- Admin-only routes:
  - Server action controls
