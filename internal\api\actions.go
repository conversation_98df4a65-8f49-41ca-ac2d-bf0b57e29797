package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"services/internal/common"
	"services/internal/helpers"
	"services/internal/server"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"gorm.io/gorm"
)

// SetAction sets an action (start, stop, reboot, terminate) for a server
// @Summary Set action on server
// @Description Set a server action like start, stop, reboot, or terminate
// @Tags servers
// @Accept json
// @Produce json
// @Param id path int true "Server ID"
// @Param action path string true "Action to perform (start, stop, reboot, terminate)"
// @Success 200 {object} Servers
// @Failure 400 {object} helpers.ErrorResponse
// @Failure 409 {object} helpers.ErrorResponse
// @Router /servers/{id}/{action} [post]
func (h Handler) SetServerAction(w http.ResponseWriter, r *http.Request) {

	ldebug := new(helpers.HelperStruct)
	ldebug.Init()
	ldebug.SetUid(r)
	ldebug.Log(helpers.Statement, "SetServerAction(+)")
	(w).Header().Set("Access-Control-Allow-Origin", "*")
	(w).Header().Set("Access-Control-Allow-Methods", "POST,OPTIONS")
	(w).Header().Set("Access-Control-Allow-Headers", " Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, credentials")
	if r.Method == http.MethodPost {
		h.mu.Lock()
		defer h.mu.Unlock()
		lId := chi.URLParam(r, "id")
		lAction := chi.URLParam(r, "action")
		log.Println("lId:", lId, ",action:", lAction)
		var lServerId server.ServerId
		if lId == "" {
			fmt.Fprint(w, helpers.GetError_String("", "no id given"))
			return
		}
		// Get the Current data of the server
		lid, _ := strconv.Atoi(lId)
		lServer, err := GetServerData(lid, h.DB)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
		log.Println("lServer:0", lServer)

		err = ValidStatus(lServer, lAction, ldebug)
		if err != nil {
			w.WriteHeader(http.StatusConflict)
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}

		lActionStatus, lUptime, err := Fsm_Transition(lServer, lAction, h.DB, ldebug, &lServerId)
		if err != nil {
			if lActionStatus == "T" {
				w.WriteHeader(http.StatusConflict)
				fmt.Fprint(w, helpers.GetError_String("", err.Error()))
				return
			}
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
		h.LogManager.AddLog(lId, lServerId)
		log.Println("h.LogManager:", h.LogManager.GetLogs(lId))
		lServer.Status = lActionStatus
		lServer.Uptime_seconds = lUptime
		ldebug.Log(helpers.Statement, "lActionStatus :", lActionStatus)
		err = json.NewEncoder(w).Encode(lServer)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprint(w, helpers.GetError_String("", "Method not Allowed"))
	}
	ldebug.Log(helpers.Statement, "SetServerAction(-)")
}

func Fsm_Transition(pServer Servers, pAction string, pdb *gorm.DB, pdebug *helpers.HelperStruct, logMng *server.ServerId) (string, int, error) {
	pdebug.Log(helpers.Statement, "Fsm_Transaction(+)")
	pdebug.Log(helpers.Statement, "pServer.Status:", pServer.Status)
	switch pServer.Status {
	case common.Stopped:
		{
			if pAction == common.Start {
				log.Println("Started server.")
				luptime, err := UpdateStatus(pServer, common.Running, pdb, pdebug)
				if err != nil {
					return "", luptime, err
				}
				logMng.ServerName = pServer.Name
				logMng.Timestamp = common.Ist_time()
				logMng.State = common.Started

				return common.Running, luptime, nil
			} else if pAction == common.Terminate {
				luptime, err := UpdateStatus(pServer, common.Terminated, pdb, pdebug)
				if err != nil {
					return "", luptime, err
				}
				log.Println("Server Terminated.")
				logMng.ServerName = pServer.Name
				logMng.Timestamp = common.Ist_time()
				logMng.State = common.Terminated
				return common.Terminated, luptime, nil
			} else {
				return "T", 0, helpers.ErrReturn(errors.New("invalid transition"))
			}
		}
	case common.Running:
		{
			if pAction == common.Stop {
				log.Println(" server Stopped .")
				luptime, err := UpdateStatus(pServer, common.Stopped, pdb, pdebug)
				if err != nil {
					return "", luptime, err
				}
				logMng.ServerName = pServer.Name
				logMng.Timestamp = common.Ist_time()
				logMng.State = common.Stopped
				return common.Stopped, luptime, nil

			} else if pAction == common.Terminate {
				log.Println("Server Terminated.")
				luptime, err := UpdateStatus(pServer, common.Terminated, pdb, pdebug)
				if err != nil {
					return "", luptime, err
				}
				logMng.ServerName = pServer.Name
				logMng.Timestamp = common.Ist_time()
				logMng.State = common.Terminated
				return common.Terminated, luptime, nil
			} else if pAction == common.Reboot {
				log.Println("Rebooted successfully.")
				luptime, err := UpdateStatus(pServer, common.Running, pdb, pdebug)
				if err != nil {
					return "", luptime, err
				}
				logMng.ServerName = pServer.Name
				logMng.Timestamp = common.Ist_time()
				logMng.State = common.Rebooted
				return common.Rebooted, luptime, nil
			} else {
				return "T", 0, helpers.ErrReturn(errors.New("invalid transition"))
			}
		}
	case common.Terminated:
		{
			log.Println("Already server terminated")
			return "T", 0, helpers.ErrReturn(errors.New("invalid transition"))
		}
	default:
		{
			return "", 0, helpers.ErrReturn(errors.New("current action is empty"))
		}
	}
	pdebug.Log(helpers.Statement, "Fsm_Transaction(-)")
	return "", 0, nil
}

func UpdateStatus(pServer Servers, pStatus string, pdb *gorm.DB, pdebug *helpers.HelperStruct) (int, error) {

	pdebug.Log(helpers.Statement, "UpdateStatus(+)")
	var data interface{}
	var lUptime int
	pdebug.Log(helpers.Statement, "Status :", pStatus)

	switch pStatus {
	case common.Running:
		{
			data = map[string]interface{}{
				"status":     pStatus,
				"updated_at": common.Ist_time(),
				// "started_at": common.Ist_time(),
				"started_at": "now()",
			}
		}
	case common.Stopped:
		{
			lUptime = TimeCalculate(pServer, pdebug)
			data = map[string]interface{}{
				"status":         pStatus,
				"updated_at":     common.Ist_time(),
				"uptime_seconds": lUptime,
			}
		}
	case common.Terminated:
		{
			if pServer.Status == common.Running {
				lUptime = TimeCalculate(pServer, pdebug)
				data = map[string]interface{}{
					"status":         pStatus,
					"updated_at":     common.Ist_time(),
					"uptime_seconds": lUptime,
					"terminated_at":  common.Ist_time(),
				}
			} else {
				data = map[string]interface{}{
					"status":        pStatus,
					"updated_at":    common.Ist_time(),
					"terminated_at": common.Ist_time(),
				}
			}

		}
	}
	pdebug.Log(helpers.Statement, "Data :", data)

	err := pdb.Debug().Table("servers").
		Where("id = ?", pServer.Id).
		Updates(data).Error
	if err != nil {
		return lUptime, helpers.ErrReturn(err)
	}
	pdebug.Log(helpers.Statement, "UpdateStatus(-)")
	return lUptime, nil
}

// calculate the uptime
func TimeCalculate(pServer Servers, pdebug *helpers.HelperStruct) int {
	pdebug.Log(helpers.Statement, "TimeCalculate(+)")

	// loc, err := time.LoadLocation("Asia/Kolkata")
	// if err != nil {
	// 	fmt.Printf("Error loading timezone 'Asia/Kolkata': %v\n", err)
	// 	return 0
	// }

	// Convert start time to India timezone
	startedTime := ConvertToISTWithoutShift(*pServer.Started_at)
	log.Println("startedTime (IST):", startedTime)

	// Current time in IST
	currentTime := time.Now()
	log.Println("currentTime (IST):", currentTime)

	// Calculate difference
	duration := currentTime.Sub(startedTime)
	log.Println("duration:", duration)

	diffInSeconds := duration.Seconds()
	log.Println("diffInSeconds:", diffInSeconds)

	diffInIntegerSeconds := int(diffInSeconds)
	log.Println("pServer.Uptime_seconds:", pServer.Uptime_seconds)
	log.Println("diffInIntegerSeconds:", diffInIntegerSeconds)
	diffInIntegerSeconds = pServer.Uptime_seconds + diffInIntegerSeconds
	log.Println("Total:", diffInIntegerSeconds)

	pdebug.Log(helpers.Statement, "TimeCalculate(-)", diffInIntegerSeconds)
	return diffInIntegerSeconds
}

func ValidStatus(pServer Servers, pStatus string, pdebug *helpers.HelperStruct) error {
	pdebug.Log(helpers.Statement, "ValidStatus(+)")
	switch pServer.Status {
	case common.Running:
		if pStatus == common.Start {
			return helpers.ErrReturn(errors.New("invalid action"))
		}
	case common.Stopped:
		if pStatus == common.Stop {
			return helpers.ErrReturn(errors.New("invalid action"))
		}
	case common.Terminated:
		if pStatus == common.Stop || pStatus == common.Start {
			return helpers.ErrReturn(errors.New("invalid action"))
		}
	}
	pdebug.Log(helpers.Statement, "ValidStatus(-)")
	return nil
}

func ConvertToISTWithoutShift(t time.Time) time.Time {
	loc, _ := time.LoadLocation("Asia/Kolkata")
	return time.Date(
		t.Year(), t.Month(), t.Day(),
		t.Hour(), t.Minute(), t.Second(), t.Nanosecond(),
		loc, // assign IST location
	)
}
