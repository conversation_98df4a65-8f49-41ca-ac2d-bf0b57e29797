package api

import (
	"log"
	"net/http"

	"services/internal/common"
	"services/internal/server"
	"sync"
	"time"

	"github.com/go-chi/chi/v5"
	httpSwagger "github.com/swaggo/http-swagger"
	"gorm.io/gorm"
)

type Handler struct {
	DB         *gorm.DB
	mu         sync.Mutex
	LogManager *server.LogManager[server.ServerId]
	Pass       *common.HashedPassword
}

// NewHandler returns a chi router with all routes
func NewHandler(db *gorm.DB, logManager *server.LogManager[server.ServerId], pass *common.HashedPassword) http.Handler {
	r := chi.NewRouter()
	log.Println("pass:", pass)
	h := &Handler{DB: db, LogManager: logManager, Pass: pass}
	// scheduler
	go Scheduler(h.DB)

	r.Get("/swagger/*", httpSwagger.WrapHandler)
	//host on -->  http://localhost:8080/swagger/index.html
	// Routes
	r.Post("/login", h.<PERSON><PERSON>)

	r.Group(func(r chi.Router) {
		r.Use(h.JWTAuthMiddleware)
		r.Post("/server", h.CreateServer)
		r.Get("/servers/{id}", h.GetServerById)
		r.Post("/servers/{id}/{action}", h.SetServerAction)
		r.Get("/servers", h.GetAllServers)
		r.Get("/servers/{id}/logs", h.GetServerLog)
	})
	return r
}

func Scheduler(db *gorm.DB) {
	log.Println("Scheduler started")
	for {
		AutoStopLongRunningServers(db)
		time.Sleep(10 * time.Minute)
	}
}

func AutoStopLongRunningServers(db *gorm.DB) error {
	// Calculate time threshold
	threshold := time.Now().Add(-30 * time.Minute)

	// Subquery: select IDs that match condition
	subQuery := db.Model(&Servers{}).
		Select("id").
		Where("status = ? AND started_at <= ?", "running", threshold)

	// Main update
	result := db.Model(&Servers{}).
		Where("id IN (?)", subQuery).
		Updates(map[string]interface{}{
			"status":     "stopped",
			"updated_by": "autobots",
		})

	return result.Error
}
