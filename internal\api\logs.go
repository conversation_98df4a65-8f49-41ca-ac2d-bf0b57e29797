package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"services/internal/common"
	"services/internal/helpers"
	"services/internal/server"

	"github.com/go-chi/chi/v5"
)

type RingRespStruc struct {
	Servers []server.ServerId
	Status  string
	Msg     string
}

// GetServerLogs returns last 100 lifecycle events for a server
// @Summary Get server logs
// @Description Retrieves last 100 state transitions (ring buffer)
// @Tags servers
// @Accept json
// @Produce json
// @Param id path string true "Server ID"
// @Success 200 {array} RingRespStruc
// @Failure 404 {object} helpers.ErrorResponse
// @Router /servers/{id}/logs [get]
func (h Handler) GetServerLog(w http.ResponseWriter, r *http.Request) {
	ldebug := new(helpers.HelperStruct)
	ldebug.Init()
	ldebug.SetUid(r)
	ldebug.Log(helpers.Statement, "GetServerLog(+)")
	(w).Header().Set("Access-Control-Allow-Origin", "*")
	(w).Header().Set("Access-Control-Allow-Methods", "GET,OPTIONS")
	(w).Header().Set("Access-Control-Allow-Headers", " Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, credentials")
	if r.Method == http.MethodGet {
		var lResponse RingRespStruc
		lResponse.Status = common.Success
		lId := chi.URLParam(r, "id")
		lServerHistory := h.LogManager.GetLogs(lId)
		// err := json.NewEncoder(w).Encode(lserver)
		// if err != nil {
		// 	fmt.Fprintf(w, `{"Msg":"error - %v"}`, lId)
		// 	return
		// }
		if len(lServerHistory) == 0 {
			lResponse.Msg = "no records"
			fmt.Fprint(w, common.ErrorResponse(lResponse))
			return
		}
		lResponse.Servers = lServerHistory
		err := json.NewEncoder(w).Encode(lResponse)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprint(w, `{"error":"Method not allowed"}`)
	}
	ldebug.Log(helpers.Statement, "GetServerLog(-)")
}
