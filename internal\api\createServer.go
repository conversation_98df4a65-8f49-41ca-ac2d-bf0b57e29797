package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"services/internal/common"
	"services/internal/helpers"
	"services/internal/server"
	"time"

	"gorm.io/gorm"
)

// Servers table struct
type Servers struct {
	Id             int           `gorm:"primaryKey;column:id"`
	Name           string        `gorm:"column:name"`
	Status         string        `gorm:"column:status"`
	Region         string        `gorm:"column:region"`
	Type           string        `gorm:"column:type"`
	Created_at     time.Time     `gorm:"column:created_at"`
	Updated_at     time.Time     `gorm:"column:updated_at"`
	Terminated_at  *time.Time    `gorm:"column:terminated_at"`
	Started_at     *time.Time    `gorm:"column:started_at"`
	Uptime_seconds int           `gorm:"column:uptime_seconds"`
	Ip_id          int           `gorm:"column:ip_id"`
	Updated_by     string        `gorm:"column:updated_by"`
	Ip_address     Allocated_ips `gorm:"foreignKey:Ip_id;references:Id"`
}

type ServerCreateReq struct {
	Name   string `json:"name" example:"server-16"`
	Region string `json:"region" example:"us-east"`
	Type   string `json:"type" example:"t2.micro"`
	Status string `json:"status" example:"stopped"`
}

// allocated_ips table struct
type Allocated_ips struct {
	Id        int    `gorm:"primaryKey;column:id"`
	Ip        string `gorm:"type:inet;column:ip"`
	Allocated bool   `gorm:"column:allocated"`
}
type CreateServeResp struct {
	ServerId int
	Status   string
	Msg      string
}

// CreateServer provisions a new virtual server
// @Summary Provision a new server
// @Description Creates a virtual server with default status "stopped" and allocates a private IP
// @Tags servers
// @Accept json
// @Produce json
// @Param server body ServerCreateReq true "Server creation payload"
// @Success 201 {object} Servers
// @Failure 400 {object} Servers
// @Router /server [post]
func (h Handler) CreateServer(w http.ResponseWriter, r *http.Request) {
	ldebug := new(helpers.HelperStruct)
	ldebug.Init()
	ldebug.SetUid(r)
	(w).Header().Set("Access-Control-Allow-Origin", "*")
	(w).Header().Set("Access-Control-Allow-Methods", "POST,OPTIONS")
	(w).Header().Set("Access-Control-Allow-Headers", " Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, credentials")
	ldebug.Log(helpers.Statement, "CreateServer(+)")
	if r.Method == http.MethodPost {
		var lResponse CreateServeResp
		lResponse.Status = common.Success
		h.mu.Lock()
		defer h.mu.Unlock()

		var lRequest ServerCreateReq
		var lServerId server.ServerId
		//Unmarshal the request
		err := json.NewDecoder(r.Body).Decode(&lRequest)
		if err != nil {
			ldebug.Log(helpers.Elog, err.Error())
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}

		// Validate required fields
		if lRequest.Name == "" {
			msg := "server name is required"
			ldebug.Log(helpers.Elog, msg)
			fmt.Fprint(w, helpers.GetError_String("", msg))
			return
		}

		// Create server object from request
		serverObj := Servers{
			Name:   lRequest.Name,
			Region: lRequest.Region,
			Type:   lRequest.Type,
		}
		log.Println("lRequest:", lRequest)

		// Logic to create the server
		lId, err := SetServer(serverObj, h.DB, ldebug)
		if err != nil {
			log.Printf("error %v", err)
			w.WriteHeader(http.StatusInternalServerError)
			if strings.Contains(err.Error(), "duplicate key value") {
				fmt.Fprintf(w, `{"Status":"error","Msg":"IP allocation failed, please try again"}`)
			} else {
				fmt.Fprintf(w, `{"Status":"error","Msg":"Failed to create server"}`)
			}
			return
		}
		
		// Fetch the complete server data with IP
		var createdServer Servers
		if err := h.DB.Preload("Ip_address").First(&createdServer, lId).Error; err != nil {
			ldebug.Log(helpers.Elog, "Failed to fetch complete server data: %v", err)
			// Return a basic success response instead of the complete server data
			w.WriteHeader(http.StatusCreated)
			lResponse.ServerId = lId
			lResponse.Status = "success"
			lResponse.Msg = "Server created successfully"
			json.NewEncoder(w).Encode(lResponse)
			return
		}
		
		// Return the complete server data
		w.WriteHeader(http.StatusCreated)
		if err := json.NewEncoder(w).Encode(createdServer); err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}

		// Fallback response if we couldn't load the complete data
		lServerId.ServerName = serverObj.Name
		lServerId.State = common.Created
		lServerId.Timestamp = common.Ist_time()
		lResponse.ServerId = lId
		h.LogManager.AddLog(strconv.Itoa(lId), lServerId)
		err = json.NewEncoder(w).Encode(lResponse)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprint(w, `{"error":"Method not allowed"}`)
	}
	ldebug.Log(helpers.Statement, "CreateServer(-)")

}

func SetServer(pData Servers, db *gorm.DB, pdebug *helpers.HelperStruct) (int, error) {
	// Begin a transaction for the entire server creation process
	tx := db.Begin()
	if tx.Error != nil {
		pdebug.Log(helpers.Elog, "Failed to begin transaction: %v", tx.Error)
		return 0, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Check if server name already exists before IP allocation
	var existingServer Servers
	if err := tx.Where("name = ?", pData.Name).First(&existingServer).Error; err == nil {
		tx.Rollback()
		return 0, fmt.Errorf("server with name '%s' already exists", pData.Name)
	} else if err != gorm.ErrRecordNotFound {
		tx.Rollback()
		pdebug.Log(helpers.Elog, "Failed to check existing server: %v", err)
		return 0, err
	}

	// First try to find an unallocated IP
	lIp_Datas, err := FetchLastInsertedIp(tx)
	if err != nil {
		tx.Rollback()
		pdebug.Log(helpers.Elog, err.Error())
		return 0, helpers.ErrReturn(err)
	}

	var allocatedIPId int
	if !lIp_Datas.Allocated {
		// Use existing unallocated IP
		if err := tx.Model(&lIp_Datas).Where("id = ?", lIp_Datas.Id).Update("allocated", true).Error; err != nil {
			tx.Rollback()
			pdebug.Log(helpers.Elog, "Failed to update IP allocation: %v", err)
			return 0, err
		}
		allocatedIPId = lIp_Datas.Id
	} else {
		// Find next available IP with proper transaction handling
		allocatedIP, err := allocateNextAvailableIP(tx, lIp_Datas.Ip, pdebug)
		if err != nil {
			tx.Rollback()
			return 0, err
		}
		allocatedIPId = allocatedIP.Id
	}

	lServer := Servers{
		Name:       pData.Name,
		Status:     common.Stopped,
		Ip_id:      allocatedIPId,
		Region:     pData.Region,
		Type:       pData.Type,
		Created_at: common.Ist_time(),
		Updated_at: common.Ist_time(),
	}

	// Create the server
	err = tx.Create(&lServer).Error
	if err != nil {
		tx.Rollback()
		pdebug.Log(helpers.Elog, err.Error())
		return 0, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		pdebug.Log(helpers.Elog, "Failed to commit transaction: %v", err)
		return 0, err
	}

	pdebug.Log(helpers.Statement, "Server created successfully with ID:", lServer.Id)
	return lServer.Id, nil
}

func allocateNextAvailableIP(tx *gorm.DB, lastIP string, pdebug *helpers.HelperStruct) (Allocated_ips, error) {
	var maxAttempts = 10
	var attempts = 0
	
	for attempts < maxAttempts {
		nextIP, err := getNextIPInTransaction(lastIP, tx, pdebug)
		if err != nil {
			return Allocated_ips{}, err
		}

		// Try to find if IP already exists
		var existingIP Allocated_ips
		err = tx.Where("ip = ?", nextIP).First(&existingIP).Error

		if err == gorm.ErrRecordNotFound {
			// IP doesn't exist, create it as allocated
			newIP := Allocated_ips{
				Ip:        nextIP,
				Allocated: true,
			}
			if err := tx.Create(&newIP).Error; err != nil {
				if strings.Contains(err.Error(), "duplicate key value") {
					lastIP = nextIP
					attempts++
					continue
				}
				return Allocated_ips{}, err
			}
			return newIP, nil
		} else if err != nil {
			return Allocated_ips{}, err
		} else if !existingIP.Allocated {
			// IP exists and is not allocated, try to allocate it
			result := tx.Model(&existingIP).Where("id = ? AND allocated = ?", existingIP.Id, false).Update("allocated", true)
			if result.Error != nil {
				return Allocated_ips{}, result.Error
			}
			if result.RowsAffected > 0 {
				return existingIP, nil
			}
			// Couldn't allocate, try next IP
		}
		
		lastIP = nextIP
		attempts++
	}

	return Allocated_ips{}, errors.New("failed to allocate IP after multiple attempts")
}

func getNextIPInTransaction(lastIP string, tx *gorm.DB, pdebug *helpers.HelperStruct) (string, error) {
	startIP := net.ParseIP("***********")
	endIP := net.ParseIP("***************")

	start := ipToInt(startIP)
	end := ipToInt(endIP)
	var current uint32
	
	if lastIP == "" {
		current = start
	} else {
		current = ipToInt(net.ParseIP(lastIP)) + 1
	}

	if current > end {
		// Try to find an unallocated IP in the range
		var freeIP Allocated_ips
		err := tx.Where("allocated = ?", false).First(&freeIP).Error
		if err != nil {
			return "", errors.New("no available IPs in range")
		}
		return freeIP.Ip, nil
	}

	return intToIP(current).String(), nil
}

// Fetch the last inserted Ip
func FetchLastInsertedIp(db *gorm.DB) (Allocated_ips, error) {
	var lIps Allocated_ips
	
	// First try to find an unallocated IP
	err := db.Where("allocated = ?", false).First(&lIps).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No unallocated IPs found, get the last allocated IP to generate next one
			err = db.Order("ip DESC").First(&lIps).Error
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					// No IPs at all, create initial IP
					lIps = Allocated_ips{
						Ip: "*********",  // Starting IP address
						Allocated: false,
					}
					if err := db.Create(&lIps).Error; err != nil {
						if strings.Contains(err.Error(), "duplicate key value") {
							// If initial IP exists, get the highest IP
							if err := db.Order("ip DESC").First(&lIps).Error; err != nil {
								return lIps, fmt.Errorf("error finding highest IP: %v", err)
							}
						} else {
							return lIps, fmt.Errorf("error creating initial IP: %v", err)
						}
					}
				} else {
					return lIps, fmt.Errorf("error fetching IPs: %v", err)
				}
			}
		} else {
			return lIps, fmt.Errorf("error checking for unallocated IPs: %v", err)
		}
	}
	
	return lIps, nil
}
