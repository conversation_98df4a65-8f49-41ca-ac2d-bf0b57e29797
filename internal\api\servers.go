package api

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"services/internal/common"
	"services/internal/helpers"
	"strconv"

	"github.com/go-chi/chi/v5"
	"gorm.io/gorm"
)

type RespStruct struct {
	Servers []Servers
	Status  string
	Msg     string
}

// GetServer retrieves metadata for a specific server
// @Summary Get server details
// @Description Retrieves full metadata including uptime
// @Tags servers
// @Accept json
// @Produce json
// @Param id path int true "Server ID"
// @Success 200 {object} Servers
// @Failure 404 {object} helpers.ErrorResponse
// @Router /servers/{id} [get]
func (h Handler) GetServerById(w http.ResponseWriter, r *http.Request) {
	ldebug := new(helpers.HelperStruct)
	ldebug.Init()
	ldebug.SetUid(r)
	ldebug.Log(helpers.Statement, "GetServerById(+)")
	(w).Header().Set("Access-Control-Allow-Origin", "*")
	(w).Header().Set("Access-Control-Allow-Methods", "GET,OPTIONS")
	(w).Header().Set("Access-Control-Allow-Headers", " Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, credentials")
	if r.Method == http.MethodGet {
		resp := common.NewResponse()
		lId := chi.URLParam(r, "id")
		if lId == "" {
			fmt.Fprint(w, helpers.GetError_String("", "no id given"))
			return
		}
		lid, _ := strconv.Atoi(lId)
		lserver, err := GetServerData(lid, h.DB)
		if err != nil {
			fmt.Fprintf(w, `{"Msg":"error - %v"}`, err)
			return
		}
		if lserver.Id == 0 {
			resp.Msg = "no server based on this id"
			fmt.Fprint(w, common.ErrorResponse(resp))
			return
		}
		err = json.NewEncoder(w).Encode(lserver)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprint(w, `{"error":"Method not allowed"}`)
	}
	ldebug.Log(helpers.Statement, "GetServerById(-)")
}

// fetch the server metadata
func GetServerData(pID int, db *gorm.DB) (pServer Servers, err error) {
	log.Println("GetServerData(+)")
	err = db.Debug().Table("servers").Where("id=?", pID).Find(&pServer).Error
	if err != nil {
		return pServer, err
	}

	log.Println("GetServerData(-)")
	return pServer, nil
}

// ListServers lists all servers with optional filters
// @Summary List servers
// @Description Lists all servers with filters for region, status, type; supports pagination and sorting
// @Tags servers
// @Accept json
// @Produce json
// @Param region query string false "Filter by region"
// @Param status query string false "Filter by status"
// @Param type query string false "Filter by type"
// @Param limit query int false "Limit number of results"
// @Param offset query int false "Offset for pagination"
// @Success 200 {array} RespStruct
// @Failure 400 {object} helpers.ErrorResponse
// @Router /servers [get]
func (h Handler) GetAllServers(w http.ResponseWriter, r *http.Request) {
	ldebug := new(helpers.HelperStruct)
	ldebug.Init()
	ldebug.SetUid(r)
	ldebug.Log(helpers.Statement, "GetAllServers(+)")
	(w).Header().Set("Access-Control-Allow-Origin", "*")
	(w).Header().Set("Access-Control-Allow-Methods", "GET,OPTIONS")
	(w).Header().Set("Access-Control-Allow-Headers", " Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, credentials")
	if r.Method == http.MethodGet {
		var lResponse RespStruct
		lResponse.Status = common.Success

		var lRequest Servers

		// Filters
		region := r.URL.Query().Get("region")
		status := r.URL.Query().Get("status")
		sType := r.URL.Query().Get("type")
		lRequest.Region = region
		lRequest.Status = status
		lRequest.Type = sType

		// Pagination
		limitStr := r.URL.Query().Get("limit")
		offsetStr := r.URL.Query().Get("offset")
		// Fetch Method
		lServers, err := FetchAllServers(lRequest, limitStr, offsetStr, h.DB, ldebug)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
		lResponse.Servers = lServers
		if len(lServers) == 0 {
			lResponse.Msg = "no records"
		}
		err = json.NewEncoder(w).Encode(lResponse)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprint(w, `{"error":"Method not allowed"}`)
	}
	ldebug.Log(helpers.Statement, "GetAllServers(-)")
}

func FetchAllServers(pServer Servers, limitStr string, offsetStr string, pdb *gorm.DB, pdebug *helpers.HelperStruct) ([]Servers, error) {
	pdebug.Log(helpers.Statement, "FetchAllServers(+)")
	var lServers []Servers
	query := pdb.Order("created_at DESC")
	if pServer.Region != "" {
		query = query.Where("region = ?", pServer.Region)
	}
	if pServer.Status != "" {
		query = query.Where("status = ?", pServer.Status)
	}
	if pServer.Type != "" {
		query = query.Where("type = ?", pServer.Type)
	}
	limit := 10 // default
	offset := 0

	if l, err := strconv.Atoi(limitStr); err == nil {
		limit = l
	}
	if o, err := strconv.Atoi(offsetStr); err == nil {
		offset = o
	}
	query = query.Limit(limit).Offset(offset)
	// Execute
	if err := query.Preload("Ip_address").Find(&lServers).Error; err != nil {
		pdebug.Log(helpers.Elog, err)
		return lServers, helpers.ErrReturn(err)
	}

	pdebug.Log(helpers.Statement, "FetchAllServers(-)")
	return lServers, nil
}

func GetIpAddress(pId int, db *gorm.DB, pdebug *helpers.HelperStruct) {
	pdebug.Log(helpers.Statement, "GetIpAddress(+)")
	// db.First(&user, 10)
	pdebug.Log(helpers.Statement, "GetIpAddress(-)")

}
