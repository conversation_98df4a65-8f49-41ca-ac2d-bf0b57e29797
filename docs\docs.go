// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/login": {
            "post": {
                "description": "Authenticates a user and returns login result by default user:admin and password:admin",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "auth"
                ],
                "summary": "User login",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "credentials",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.LoginStruc"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.LoginRespStruc"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/helpers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/server": {
            "post": {
                "description": "Creates a virtual server with default status \"stopped\" and allocates a private IP",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "servers"
                ],
                "summary": "Provision a new server",
                "parameters": [
                    {
                        "description": "Server creation payload",
                        "name": "server",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.ServerCreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/api.Servers"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/api.Servers"
                        }
                    }
                }
            }
        },
        "/servers": {
            "get": {
                "description": "Lists all servers with filters for region, status, type; supports pagination and sorting",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "servers"
                ],
                "summary": "List servers",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by region",
                        "name": "region",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by type",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Limit number of results",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.RespStruct"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/helpers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/servers/{id}": {
            "get": {
                "description": "Retrieves full metadata including uptime",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "servers"
                ],
                "summary": "Get server details",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Server ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.Servers"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/helpers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/servers/{id}/logs": {
            "get": {
                "description": "Retrieves last 100 state transitions (ring buffer)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "servers"
                ],
                "summary": "Get server logs",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Server ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.RingRespStruc"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/helpers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/servers/{id}/{action}": {
            "post": {
                "description": "Set a server action like start, stop, reboot, or terminate",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "servers"
                ],
                "summary": "Set action on server",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Server ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Action to perform (start, stop, reboot, terminate)",
                        "name": "action",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.Servers"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/helpers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/helpers.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "api.Allocated_ips": {
            "type": "object",
            "properties": {
                "allocated": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "ip": {
                    "type": "string"
                }
            }
        },
        "api.LoginRespStruc": {
            "type": "object",
            "properties": {
                "msg": {
                    "type": "string",
                    "example": "Login successful"
                },
                "status": {
                    "type": "string",
                    "example": "success"
                }
            }
        },
        "api.LoginStruc": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string",
                    "example": "admin"
                },
                "user": {
                    "type": "string",
                    "example": "admin"
                }
            }
        },
        "api.RespStruct": {
            "type": "object",
            "properties": {
                "msg": {
                    "type": "string"
                },
                "servers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Servers"
                    }
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "api.RingRespStruc": {
            "type": "object",
            "properties": {
                "msg": {
                    "type": "string"
                },
                "servers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/server.ServerId"
                    }
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "api.ServerCreateReq": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "example": "server-16"
                },
                "region": {
                    "type": "string",
                    "example": "us-east"
                },
                "status": {
                    "type": "string",
                    "example": "stopped"
                },
                "type": {
                    "type": "string",
                    "example": "t2.micro"
                }
            }
        },
        "api.Servers": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "ip_address": {
                    "$ref": "#/definitions/api.Allocated_ips"
                },
                "ip_id": {
                    "description": "Ip_address     string ` + "`" + `gorm:\"type:inet\"` + "`" + `",
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                },
                "started_at": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "terminated_at": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "uptime_seconds": {
                    "type": "integer"
                }
            }
        },
        "helpers.ErrorResponse": {
            "type": "object",
            "properties": {
                "msg": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "statusCode": {
                    "type": "string"
                }
            }
        },
        "server.ServerId": {
            "type": "object",
            "properties": {
                "serverName": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "timestamp": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/",
	Schemes:          []string{},
	Title:            "Spacekayak Virtual Server API",
	Description:      "Simulates virtual server lifecycle, billing, and state transitions",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	// LeftDelim:        "{{",
	// RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
