package api

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"services/internal/common"
	"services/internal/helpers"
	"time"

	"github.com/golang-jwt/jwt"
	"golang.org/x/crypto/bcrypt"
)

type LoginStruc struct {
	User     string `json:"user" example:"admin"`
	Password string `json:"password" example:"admin"`
}

type LoginRespStruc struct {
	Status string `json:"status" example:"success"`
	Msg    string `json:"msg" example:"Login successful"`
}

// Login authenticates a user and returns a status message
// @Summary User login
// @Description Authenticates a user and returns login result by default user:admin and password:admin
// @Tags auth
// @Accept json
// @Produce json
// @Param credentials body LoginStruc true "Login credentials"
// @Success 200 {object} LoginRespStruc
// @Failure 401 {object} helpers.ErrorResponse
// @Router /login [post]
func (h Handler) Login(w http.ResponseWriter, r *http.Request) {
	ldebug := new(helpers.HelperStruct)
	ldebug.Init()
	ldebug.SetUid(r)
	(w).Header().Set("Access-Control-Allow-Origin", "*")
	(w).Header().Set("Access-Control-Allow-Methods", "POST,OPTIONS")
	(w).Header().Set("Access-Control-Allow-Headers", " Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, credentials")
	ldebug.Log(helpers.Statement, "CreateServer(+)")
	if r.Method == http.MethodPost {
		var lResponse LoginRespStruc
		lResponse.Status = common.Success
		h.mu.Lock()
		defer h.mu.Unlock()
		var lRequest LoginStruc
		err := json.NewDecoder(r.Body).Decode(&lRequest)
		if err != nil {
			ldebug.Log(helpers.Elog, err.Error())
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
		log.Println("lResquest:", lRequest)

		if lRequest.User != common.User {
			w.WriteHeader(http.StatusUnauthorized)
			fmt.Fprint(w, helpers.GetError_String("Unauthorized", "invalid userId or password"))

		}
		// Compare entered password with hashed one
		err = bcrypt.CompareHashAndPassword([]byte(h.Pass.Password), []byte(lRequest.Password))
		if err != nil {
			log.Println("Login failed:", err)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Create JWT token
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
			"sub": lRequest.User,
			"exp": time.Now().Add(time.Hour * 24 * 30).Unix(),
		})
		tokenString, err := token.SignedString([]byte(os.Getenv("SECRET")))
		if err != nil {
			log.Println("Token signing error:", err)
			http.Error(w, "Internal error", http.StatusInternalServerError)
			return
		}

		// Set cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "Authorization",
			Value:    tokenString,
			Path:     "/",
			MaxAge:   3600 * 24 * 30,
			HttpOnly: true,
			SameSite: http.SameSiteLaxMode,
		})
		lResponse.Msg = "Logged Successfully"
		err = json.NewEncoder(w).Encode(lResponse)
		if err != nil {
			fmt.Fprint(w, helpers.GetError_String("", err.Error()))
			return
		}
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
		fmt.Fprint(w, `{"error":"Method not allowed"}`)
	}
	ldebug.Log(helpers.Statement, "CreateServer(-)")

}
